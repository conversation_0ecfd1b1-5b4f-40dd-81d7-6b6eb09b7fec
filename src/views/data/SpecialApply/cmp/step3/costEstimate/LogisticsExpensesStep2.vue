<!--
 * @Author: 张文轩
 * @Date: 2024-06-27 17:22:03
 * @LastEditTime: 2024-10-14 11:32:40
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step3\TabStep5.vue
 * 记得注释
-->
<template>
  <div class="tab-step5">
    <!-- 币种信息显示 -->
    <div class="currency-info">
      <span class="label">币种：</span>
      <span class="value">{{ allCurrencies }}</span>
      <el-button type="text" @click="showExchangeRateDialog" class="exchange-rate-btn">
        查看汇率
      </el-button>
    </div>

    <div>
      <el-table v-loading="loading" :data="tableData" size="mini" :border="false">
        <el-table-column type="index" label="行号" width="50"> </el-table-column>
        <el-table-column label="机型" minWidth="80">
          <template #default="scoped">
            {{ scoped.row.specialApplyMatLine.prodMdlCode }}
          </template>
        </el-table-column>
        <el-table-column label="整机物料号" minWidth="96">
          <template #default="scoped">
            <div class="table-cell">
              <!-- <div class="table-cell-icon">
                <i class="iconfont icon-ic_pin_incline" />
              </div> -->
              <div>{{ scoped.row.specialApplyMatLine.matCd }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="台量" minWidth="50">
          <template #default="scoped">
            {{ scoped.row.specialApplyMatLine.qty }}
          </template>
        </el-table-column>
        <el-table-column label="单台费用" align="center">
          <el-table-column label="国内段运杂费" minWidth="120" align="right">
            <template #default="{ row }">
              <div>
                <div
                  v-for="fee in getDomesticFeesByCurrency(row).fees"
                  :key="fee.currencyId"
                  style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 2px;"
                >
                  <el-tooltip
                    v-if="getDomesticFeesByCurrency(row).tip"
                    :content="getDomesticFeesByCurrency(row).tip"
                    :open-delay="1000"
                    effect="light"
                    placement="bottom-start"
                  >
                    <i class="el-icon-info icon-info" style="margin-right: 4px;"></i>
                  </el-tooltip>
                  <div style="flex-shrink: 0">
                    {{ fee.symbol }}
                  </div>
                  <div v-if="!getDomesticFeesByCurrency(row).tip">
                    {{ fee.amount | numberFilter('--', 2, true) }}
                  </div>
                  <div v-else>
                    --
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 自营-买断才有国际段运杂费 -->
          <el-table-column
            v-if="
              baseEntity.exportTypeId === ExportType.self &&
                baseEntity.saleMode === GuidePriceType.buyOut
            "
            label="国际段运杂费"
            minWidth="120"
            align="right"
          >
            <template #default="{ row }">
              <div>
                <div
                  v-for="fee in getInternationalFeesByCurrency(row).fees"
                  :key="fee.currencyId"
                  style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 2px;"
                >
                  <el-tooltip
                    v-if="getInternationalFeesByCurrency(row).tip"
                    :content="getInternationalFeesByCurrency(row).tip"
                    :open-delay="1000"
                    effect="light"
                    placement="bottom-start"
                  >
                    <i class="el-icon-info icon-info" style="margin-right: 4px;"></i>
                  </el-tooltip>
                  <div style="flex-shrink: 0">
                    {{ fee.symbol }}
                  </div>
                  <div v-if="!getInternationalFeesByCurrency(row).tip">
                    {{ fee.amount | numberFilter('--', 2, true) }}
                  </div>
                  <div v-else>
                    --
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="费用合计" minWidth="120" align="right">
            <template #default="{ row }">
              <div>
                <div
                  v-for="fee in getTotalAmount(row).fees"
                  :key="fee.currencyId"
                  style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 2px;"
                >
                  <el-tooltip
                    v-if="getTotalAmount(row).tip"
                    :content="getTotalAmount(row).tip"
                    :open-delay="1000"
                    effect="light"
                    placement="bottom-start"
                  >
                    <i class="el-icon-info icon-info" style="margin-right: 4px;"></i>
                  </el-tooltip>
                  <div style="flex-shrink: 0">
                    {{ fee.symbol }}
                  </div>
                  <div v-if="!getTotalAmount(row).tip">
                    {{ fee.amount | numberFilter('--', 2, true) }}
                  </div>
                  <div v-else>
                    --
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            v-for="item in feeTypeList"
            :key="item.name"
            :label="item.name"
            minWidth="120"
            align="right"
          >
            <template #default="scoped">
              <div style="display: flex; justify-content: flex-end;">
                <div style="flex-shrink: 0;">
                  {{ baseEntity.currencySymbol }}
                </div>
                <div>
                  {{ scoped.row[item.name] | numberFilter('--', 2, true) }}
                </div>
              </div>
            </template>
          </el-table-column> -->
        </el-table-column>
      </el-table>
    </div>

    <!-- 汇率弹窗组件 -->
    <ExchangeRateDialog
      :visible.sync="exchangeRateDialogVisible"
      :exchange-rate-data="exchangeRateData"
      @close="exchangeRateDialogVisible = false"
    />
  </div>
</template>

<script src="./LogisticsExpensesStep2.ts"></script>

<style lang="scss" scoped>
.tab-step5 {
  min-height: 282px;
  overflow-y: auto;

  .currency-info {
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    .label {
      margin-right: 8px;
    }

    .value {
      margin-right: 8px;
    }

    .exchange-rate-btn {
      padding: 0;
      font-size: 14px;
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

.icon-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #fb606d;
  margin-right: 4px;
}
</style>
