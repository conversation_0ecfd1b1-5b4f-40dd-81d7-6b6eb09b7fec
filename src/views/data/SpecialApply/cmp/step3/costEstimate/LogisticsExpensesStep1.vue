<template>
  <div class="logistics-expenses-step1">
    <!-- 表格上分配置 -->
    <div class="material-config">
      <div v-if="isSelfMode">
        <el-popover placement="top-start" width="250" trigger="manual" v-model="popoverVisible">
          <div style="margin-bottom: 8px">
            <search-remote
              v-model="defaultFreightPortSelect"
              placeholder="输入搜索或选择"
              valueKey="code"
              :queryMethod="queryFreightPort"
              :valueFormat="freightPortValueFormat"
              :labelFormat="freightPortLabelFormat"
              :multiple="false"
              :lazyLoad="true"
              size="mini"
              style="width: 100%"
              @change="handleDefaultPortSelectChange"
            />
          </div>
          <div style="text-align: right; margin: 0">
            <el-button size="mini" type="text" @click="popoverVisible = false">取消</el-button>
            <el-button type="primary" size="mini" @click="handleDefaultFreightPortChange">
              确定
            </el-button>
          </div>
          <span slot="reference" class="default-port-text" @click="popoverVisible = true">
            统一设置默认起运港/起运站
          </span>
        </el-popover>
      </div>
      <div v-else>
        <!-- 供货模式下，币种默认为CNY -->
        <span>币种：CNY</span>
      </div>
    </div>

    <!-- 物料明细表格 -->
    <div class="material-table">
      <el-form ref="form" :model="{ tableData }">
        <el-table :data="tableData" size="mini" style="width: 100%" ref="tableComponent">
          <el-table-column type="index" label="行号" width="50" fixed="left"></el-table-column>
          <el-table-column label="整机物料信息" minWidth="180" fixed="left">
            <template slot-scope="scope">
              <div class="table-cell">
                <div>物料号：{{ scope.row.matLine && scope.row.matLine.matCd }}</div>
                <div>国际产品线：{{ scope.row.matLine && scope.row.matLine.i18ProdGroupName }}</div>
                <div>机型：{{ scope.row.matLine && scope.row.matLine.prodMdlCode }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）" minWidth="176">
            <template slot-scope="scope">
              <el-tooltip
                :disabled="!scope.row.matLine || !scope.row.matLine.matDesc"
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="scope.row.matLine && scope.row.matLine.matDesc"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ scope.row.matLine && scope.row.matLine.matDesc }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）_英文" minWidth="224">
            <template slot-scope="scope">
              <el-tooltip
                :disabled="!scope.row.matLine || !scope.row.matLine.matDescEn"
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="scope.row.matLine && scope.row.matLine.matDescEn"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ scope.row.matLine && scope.row.matLine.matDescEn }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="台量" minWidth="80" align="center">
            <template>
              1
            </template>
          </el-table-column>
          <el-table-column label="发货地/出发地" minWidth="150">
            <template slot-scope="scope">
              <el-form-item
                class="table-form-item"
                :prop="`tableData.${scope.$index}.domesticFreightLandCode`"
                :rules="[
                  { required: true, message: '请选择发货地/出发地', trigger: ['blur', 'change'] },
                ]"
              >
                <search-remote
                  v-model="scope.row.domesticFreightLandCode"
                  placeholder="输入搜索或选择"
                  valueKey="code"
                  :queryMethod="queryFreightLand"
                  :valueFormat="freightLandValueFormat"
                  :labelFormat="freightLandLabelFormat"
                  :multiple="false"
                  :lazyLoad="true"
                  size="mini"
                  style="width: 100%"
                  @change="(value) => handleFreightLandChange(value, scope.row)"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="isSelfMode" label="起运港/起运站" minWidth="150">
            <template slot-scope="scope">
              <el-form-item
                class="table-form-item"
                :prop="`tableData.${scope.$index}.domesticFreightPortCode`"
                :rules="[
                  { required: true, message: '请选择起运港/起运站', trigger: ['blur', 'change'] },
                ]"
              >
                <search-remote
                  v-model="scope.row.domesticFreightPortCode"
                  placeholder="输入搜索或选择"
                  valueKey="code"
                  :queryMethod="queryFreightPort"
                  :valueFormat="freightPortValueFormat"
                  :labelFormat="freightPortLabelFormat"
                  :multiple="false"
                  :lazyLoad="true"
                  size="mini"
                  style="width: 100%"
                  @change="(value) => handleFreightPortChange(value, scope.row)"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <!-- 供货场景显示装箱费 -->
          <el-table-column v-if="isSupplyMode" label="装箱费/台" minWidth="120">
            <template slot-scope="scope">
              <div
                v-for="currency in scope.row.currencyIds"
                :key="currency.currencyId"
                class="currency-input"
                style="display: flex; align-items: center;"
              >
                <span class="currency-label" style="margin-right: 4px;">{{
                  currency.currencySymbol
                }}</span>
                <thous-number
                  style="width: 100px"
                  :value="getPackingFee(scope.row, currency)"
                  @input="setPackingFee(scope.row, currency, $event)"
                  :precision="0"
                  size="mini"
                  :min="0"
                  placeholder=""
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
  </div>
</template>

<script src="./LogisticsExpensesStep1.ts"></script>

<style lang="scss" scoped>
.logistics-expenses-step1 {
  padding: 16px;

  .material-config {
    margin-bottom: 16px;
  }

  .material-table {
    .currency-input {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .currency-label {
        font-size: 12px;
        margin-right: 4px;
      }
    }
  }

  // 表单项样式调整
  .table-form-item {
    margin-bottom: 0;

    /deep/ .el-form-item__error {
      position: static;
      padding-top: 4px;
      font-size: 12px;
      line-height: 1.2;
    }
  }
}

.default-port-text {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    color: #66b1ff;
  }
}
</style>
