<template>
  <div class="tab-step1">
    <div class="tab-step1-top">
      <div class="fee-item" v-if="showOtherExpensesOption">
        <div class="fee-item-label">
          <span>报价是否包含以下费用项：</span>
          <el-radio-group :value="otherExpenses" @input="handleOtherExpensesChange">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </div>
        <div class="fee-item-content">
          物流费用、延保费、客户佣金、赠送配件金额、额外费用、远期贴现费、信保费、国内运输保险费、国际运输保险费
        </div>
      </div>
      <div class="tab-step1-text">
        <div class="tab-step1-text-set">请填写特价申请说明</div>
        <el-dropdown placement="bottom-start" @command="doFill">
          <span class="el-dropdown-link"> 点击填写 </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="copy">复制特价申请报告</el-dropdown-item>
            <el-dropdown-item command="fill">填写特价申请报告</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="tab-step1-main">
      <el-form ref="form" :model="{ tableData }">
        <el-table ref="table" :data="tableData" :span-method="spanMethod" border>
          <el-table-column label="整机物料号" minWidth="180" fixed="left">
            <template #default="scoped">
              <div>物料号：{{ scoped.row.specialApplyMat.matCd }}</div>
              <div>国际产品线：{{ scoped.row.specialApplyMat.i18ProdGroupName }}</div>
              <div>机型： {{ scoped.row.specialApplyMat.prodMdlCode }}</div>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）" minWidth="176">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                    ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDesc)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）_英文" minWidth="224">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                    ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDescEn)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="特价适用范围" minWidth="180">
            <template #default="scope">
              <el-tooltip
                :disabled="ObjectUtil.isNullOrBlank(scope.row.applicationScope)"
                popper-class="popper-class"
                class="item"
                effect="dark"
                placement="top-start"
              >
                <!-- 换行显示 -->
                <div
                  slot="content"
                  style="white-space: pre-line"
                  v-html="formatSpecialPriceApplicableArea(scope.row)"
                ></div>
                <div class="table-cell">
                  <div>子公司： {{ scope.row.applicationScope.sbsdyName }}</div>
                  <div v-if="baseEntity.specialType != 'adjustZoneGuidePrice'">
                    办事处：{{ scope.row.applicationScope.ofcName }}
                  </div>
                  <div class="text-ellipsis">
                    销往国：{{ formatCtryName(scope.row.applicationScope) }}
                  </div>
                  <div v-if="baseEntity.specialType == 'importantProjects'">
                    客户：{{ scope.row.applicationScope.custName }}
                  </div>
                  <div>币种：{{ scope.row.applicationScope.currencyId }}</div>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="台量" minWidth="134">
            <template #default="scoped">
              <div>
                <el-radio-group
                  v-model="scoped.row.limitQty"
                  @change="limitQtyChange(scoped.row, scoped.$index)"
                >
                  <el-radio
                    :label="false"
                    :disabled="baseEntity.specialType === SpecialType.sampleMachine"
                    >不限量</el-radio
                  >
                  <el-radio :label="true">限量</el-radio>
                </el-radio-group>
                <div
                  v-if="scoped.row.limitQty"
                  style="margin-top: 8px; display: flex; justify-content: center; align-items: center;"
                >
                  <number-input
                    v-model.number="scoped.row.qty"
                    size="mini"
                    xType="num"
                    :min="1"
                    :max="999999"
                    style="width: 80px"
                    placeholder="台量"
                    @change="qtyChange(scoped.row, scoped.$index)"
                  />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="设备指导价/台" prop="exwPrice" minWidth="120">
            <template #default="scoped">
              <div v-if="scoped.row.exwPrice > 0" style="text-align: right">
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">
                  {{ scoped.row.exwPrice | numberFilter('--', 0, true) }}
                </div>
              </div>
              <div v-else style="text-align: right">
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">0</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="134">
            <template #header>
              <span>调价幅度%</span>
              <el-tooltip
                content="系统自动计算，调价幅度=调价差值/EXW设备指导价%"
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <div style="text-align: center; color: #606266;">
                {{ formatAdjustRange(scoped.row) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="134">
            <template #header>
              <span>调价差值</span>
              <el-tooltip
                :content="
                  otherExpenses
                    ? '系统自动计算，调价差值=EXW设备指导价-EXW设备特价'
                    : '基于年度指导价设置一个调价差值，仅支持输入正整数，EXW设备特价=EXW设备指导价-调价差值'
                "
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <!-- 场景1：不包含物流费用，调价差值可编辑 -->
              <el-form-item
                v-if="!otherExpenses"
                class="table-form-item"
                :prop="`tableData.${scoped.$index}.differAmt`"
                :rules="[
                  { required: true, message: '请输入调价差值', trigger: ['blur', 'change'] },
                ]"
              >
                <div style="display: flex; align-items: center;">
                  <span style="margin-right: 4px;">{{
                    scoped.row.applicationScope.currencySymbol
                  }}</span>
                  <thous-number
                    style="width: 100px"
                    :value="
                      typeof scoped.row.differAmt === 'string'
                        ? Number(scoped.row.differAmt)
                        : scoped.row.differAmt
                    "
                    @input="scoped.row.differAmt = $event"
                    :precision="0"
                    size="mini"
                    :min="0"
                    :max="scoped.row.exwPrice - 1"
                    placeholder=""
                    @change="differAmtChange(scoped.row, scoped.$index)"
                    @blur="$refs.form.validateField(`tableData.${scoped.$index}.differAmt`)"
                  />
                </div>
              </el-form-item>
              <!-- 场景2：包含物流费用，差值只读 -->
              <div v-else style="text-align: right;">
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">
                  {{ scoped.row.differAmt | numberFilter('--', 0, true) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="160">
            <template #header>
              <span>EXW设备特价/台</span>
              <div style="font-size: 12px; color: #f56c6c; font-weight: normal;">
                （不包含预估费用）
              </div>
              <el-tooltip
                :content="
                  otherExpenses
                    ? 'EXW设备特价/台=预估综合特价/台-预估物流费用/台-预估其他费用/台'
                    : '仅支持输入正整数，EXW设备特价=EXW设备指导价-调价差值'
                "
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <!-- 场景1：不包含物流费用，设备特价可编辑 -->
              <div v-if="!otherExpenses" style="display: flex; align-items: center;">
                <span style="margin-right: 4px;">{{
                  scoped.row.applicationScope.currencySymbol
                }}</span>
                <thous-number
                  style="width: 100px"
                  :value="
                    typeof scoped.row.specialPrice === 'string'
                      ? Number(scoped.row.specialPrice)
                      : scoped.row.specialPrice
                  "
                  @input="scoped.row.specialPrice = $event"
                  :precision="0"
                  size="mini"
                  :min="1"
                  :max="scoped.row.exwPrice"
                  @change="specialPriceChange(scoped.row, scoped.$index)"
                />
              </div>
              <!-- 场景2：包含物流费用，设备特价只读且可能显示错误信息 -->
              <div v-else style="text-align: right;">
                <div
                  style="display: inline-block"
                  :style="{
                    color: tableErrorFlag[scoped.$index] ? '#f56c6c' : '#606266',
                  }"
                >
                  <span v-if="tableErrorFlag[scoped.$index]">
                    {{ tableErrorMsg[scoped.$index] }}
                  </span>
                  <span v-else>
                    {{ scoped.row.specialPrice | numberFilter('--', 0, true) }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 场景2：包含物流费用 -->
          <el-table-column v-if="otherExpenses" label="预估费用">
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center; gap: 24px;">
                <div style="display: flex; align-items: center;">
                  <span style="color: #f56c6c; margin-right: 4px;">*</span>
                  <span style="font-weight: bold;">预估费用</span>
                </div>
                <span class="el-dropdown-link" @click="openFeeEstimation">费用估算</span>
                <span class="el-dropdown-link" @click="handleClearFeeData">清空填写内容</span>
              </div>
            </template>

            <!-- 预估物流费用/台 -->
            <el-table-column label="预估物流费用/台" minWidth="160">
              <template #header>
                <div style="text-align: center;">
                  <span>预估物流费用/台</span>
                  <el-tooltip content="预估费用中计算的物流费用合计" placement="top-start">
                    <i class="el-icon-info icon-info"></i>
                  </el-tooltip>
                </div>
              </template>
              <template #default="scoped">
                <div style="text-align: right;">
                  <div style="display: inline-block">
                    {{ scoped.row.applicationScope.currencySymbol }}
                  </div>
                  <div style="display: inline-block">
                    {{ scoped.row.logisticsExpenses | numberFilter('--', 0, true) }}
                  </div>
                </div>
              </template>
            </el-table-column>

            <!-- 预估其他费用/台 -->
            <el-table-column label="预估其他费用/台" minWidth="160">
              <template #header>
                <div style="text-align: center;">
                  <span>预估其他费用/台</span>
                  <el-tooltip content="预估费用中计算的其他费用合计" placement="top-start">
                    <i class="el-icon-info icon-info"></i>
                  </el-tooltip>
                </div>
              </template>
              <template #default="scoped">
                <div style="text-align: right;">
                  <div style="display: inline-block">
                    {{ scoped.row.applicationScope.currencySymbol }}
                  </div>
                  <div style="display: inline-block">
                    {{ scoped.row.otherExpenses | numberFilter('--', 0, true) }}
                  </div>
                </div>
              </template>
            </el-table-column>

            <!-- 预估综合特价/台 -->
            <el-table-column label="预估综合特价/台" minWidth="160">
              <template #header>
                <div style="text-align: center;">
                  <span>预估综合特价/台</span>
                  <el-tooltip
                    content="限制输入正整数，EXW设备特价/台=预估综合特价/台-预估物流费用/台-预估其他费用/台"
                    placement="top-start"
                  >
                    <i class="el-icon-info icon-info"></i>
                  </el-tooltip>
                </div>
              </template>
              <template #default="scoped">
                <el-form-item
                  class="table-form-item"
                  :prop="`tableData.${scoped.$index}.total`"
                  :rules="[
                    { required: true, message: '请输入预估综合特价', trigger: ['blur', 'change'] },
                  ]"
                >
                  <div style="display: flex; align-items: center;">
                    <span style="margin-right: 4px;">{{
                      scoped.row.applicationScope.currencySymbol
                    }}</span>
                    <thous-number
                      style="width: 100px"
                      :value="
                        typeof scoped.row.total === 'string'
                          ? Number(scoped.row.total)
                          : scoped.row.total
                      "
                      @input="scoped.row.total = $event"
                      :precision="0"
                      size="mini"
                      :min="1"
                      placeholder=""
                      @change="totalChange(scoped.row, scoped.$index)"
                      @blur="$refs.form.validateField(`tableData.${scoped.$index}.total`)"
                    />
                  </div>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <!-- 特价申请说明 -->
    <SpecialApplyDescriptionDrawer
      ref="specialApplyDrawer"
      v-model="showFillDrawer"
      :baseEntity="baseEntity"
      :specialApplyDescription="specialApplyDescription"
      @confirm="changeSpecialApplyDescription"
    >
    </SpecialApplyDescriptionDrawer>
    <!-- 费用估算 -->
    <SpecialApplyCostEstimate
      v-if="showCostEstimate"
      ref="specialApplyCostEstimate"
      v-model="showCostEstimate"
      :baseEntity="baseEntity"
      :specialApplyDescription="specialApplyDescription"
      @refresh="refreshBaseEntity"
      @confirm="refreshBaseEntity"
    ></SpecialApplyCostEstimate>
  </div>
</template>

<script src="./TabStep1.ts"></script>

<style lang="scss" scoped>
.tab-step1 {
  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .fee-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      &-label {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 8px;
        span {
          font-weight: bold;
          margin-right: 32px;
        }
      }
    }
    .tab-step1-text {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex: 1;
      &-set {
        font-size: 12px;
        color: #242633;
        margin-right: 8px;
      }
    }
  }
  &-main {
    .icon-info {
      margin-top: 4px;
      color: #a1b0c8;
      font-size: 14px;
      margin: 0px 4px;
      &:hover {
        color: $--color-primary;
      }
    }
  }
  /deep/ .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    font-size: 12px;
  }
  /deep/ .el-radio-group {
    .el-radio {
      margin-right: 10px;
      .el-radio__label {
        font-size: 12px;
        font-weight: normal;
        color: #606266 !important;
      }
    }
  }

  // 表单项样式调整
  .table-form-item {
    margin-bottom: 0;

    /deep/ .el-form-item__error {
      position: static;
      padding-top: 4px;
      font-size: 12px;
      line-height: 1.2;
    }
  }
}
</style>
