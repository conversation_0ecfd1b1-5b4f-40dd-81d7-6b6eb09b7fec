<!--
 * @Author: 张文轩
 * @Date: 2024-08-29 16:38:05
 * @LastEditTime: 2024-12-04 14:10:34
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\SpecialApply\cmp\drawer\AgainCheckDialog.vue
 * 记得注释
-->
<template>
  <el-dialog :title="title" width="80%" @close="handleCancel" :visible="true">
    <div class="content">
      <el-form size="mini" :model="{ tableData }" :rules="rules" ref="form">
        <el-table ref="table" :data="tableData" :span-method="spanMethod" :border="false">
          <el-table-column label="整机物料号" minWidth="180" fixed="left">
            <template #default="scoped">
              <div>物料号：{{ scoped.row.specialApplyMat.matCd }}</div>
              <div>国际产品线：{{ scoped.row.specialApplyMat.i18ProdGroupName }}</div>
              <div>机型： {{ scoped.row.specialApplyMat.prodMdlCode }}</div>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）" minWidth="176">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                    ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDesc)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）_英文" minWidth="224">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                    ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDescEn)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="特价适用范围" minWidth="180">
            <template #default="scope">
              <el-tooltip
                :disabled="ObjectUtil.isNullOrBlank(scope.row.applicationScope)"
                popper-class="popper-class"
                class="item"
                effect="dark"
                placement="top-start"
              >
                <!-- 换行显示 -->
                <div
                  slot="content"
                  style="white-space: pre-line"
                  v-html="formatSpecialPriceApplicableArea(scope.row)"
                ></div>
                <div class="table-cell">
                  <div>子公司： {{ scope.row.applicationScope.sbsdyName }}</div>
                  <div v-if="specialType != 'adjustZoneGuidePrice'">
                    办事处：{{ scope.row.applicationScope.ofcName }}
                  </div>
                  <div class="text-ellipsis">
                    销往国：{{ formatCtryName(scope.row.applicationScope) }}
                  </div>
                  <div v-if="specialType == 'importantProjects'">
                    客户：{{ scope.row.applicationScope.custName }}
                  </div>
                  <div>币种：{{ scope.row.applicationScope.currencyId }}</div>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="台量" minWidth="134">
            <template #default="scoped">
              <div v-if="scoped.row.limitQty">
                {{ scoped.row.qty }}
              </div>
              <div v-else>
                不限量
              </div>
            </template>
          </el-table-column>
          <el-table-column label="价格有效期" minWidth="160">
            <template #default="scoped">
              <div>
                {{ scoped.row.beginDate + '~' + scoped.row.endDate }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="设备指导价/台" prop="exwPrice" minWidth="120">
            <template #default="scoped">
              <div v-if="scoped.row.exwPrice > 0" style="text-align: right">
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">
                  {{ scoped.row.exwPrice | numberFilter('--', 0, true) }}
                </div>
              </div>
              <div v-else style="text-align: right">
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">0</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="134">
            <template #header>
              <span>调价幅度%</span>
              <el-tooltip
                content="系统自动计算，调价幅度=调价差值/EXW设备指导价%"
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <div style="text-align: center; color: #606266;">
                {{ formatAdjustRange(scoped.row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 调价差值列 -->
          <el-table-column minWidth="134">
            <template #header>
              <span>调价差值</span>
              <el-tooltip
                :content="
                  otherExpenses
                    ? '系统自动计算，调价差值=EXW设备指导价-EXW设备特价'
                    : '基于年度指导价设置一个调价差值，仅支持输入正整数，EXW设备特价=EXW设备指导价-调价差值'
                "
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <!-- 场景1：不包含物流费用，调价差值可编辑 -->
              <el-form-item
                v-if="!otherExpenses"
                :prop="'tableData.' + scoped.$index + '.differAmt'"
                :rules="{ required: true, message: '请输入调价差值', trigger: 'blur' }"
              >
                <div style="display: flex; align-items: center;">
                  <span style="margin-right: 4px;">
                    {{ scoped.row.applicationScope.currencySymbol }}
                  </span>
                  <thous-number
                    style="width: 100px"
                    :value="
                      typeof scoped.row.differAmt === 'string'
                        ? Number(scoped.row.differAmt)
                        : scoped.row.differAmt
                    "
                    @input="scoped.row.differAmt = $event"
                    :precision="0"
                    size="mini"
                    :min="0"
                    :max="scoped.row.exwPrice - 1"
                    placeholder=""
                    @change="differAmtChange(scoped.row, scoped.$index)"
                  />
                </div>
              </el-form-item>
              <!-- 场景2：包含物流费用，差值只读 -->
              <div v-else>
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">
                  {{ scoped.row.differAmt | numberFilter('--', 0, true) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="160">
            <template #header>
              <span>EXW设备特价/台</span>
              <el-tooltip
                :content="
                  otherExpenses
                    ? 'EXW设备特价/台=预估综合特价/台-预估物流费用/台-预估其他费用/台'
                    : '仅支持输入正整数，EXW设备特价=EXW设备指导价-调价差值'
                "
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <!-- 不包含其他费用时：可编辑 -->
              <el-form-item
                v-if="!otherExpenses"
                :prop="'tableData.' + scoped.$index + '.specialPrice'"
              >
                <div style="display: flex; align-items: center;">
                  <span style="margin-right: 4px;">
                    {{ scoped.row.applicationScope.currencySymbol }}
                  </span>
                  <thous-number
                    style="width: 100px"
                    :value="
                      typeof scoped.row.specialPrice === 'string'
                        ? Number(scoped.row.specialPrice)
                        : scoped.row.specialPrice
                    "
                    @input="scoped.row.specialPrice = $event"
                    :precision="0"
                    size="mini"
                    :min="1"
                    :max="scoped.row.exwPrice"
                    @change="specialPriceChange(scoped.row, scoped.$index)"
                  />
                </div>
              </el-form-item>
              <!-- 场景2：包含物流费用，设备特价只读且可能显示错误信息 -->
              <div v-else>
                <div
                  style="display: inline-block"
                  :style="{
                    color: tableErrorFlag[scoped.$index] ? '#f56c6c' : '#606266',
                  }"
                >
                  <span v-if="tableErrorFlag[scoped.$index]">
                    {{ tableErrorMsg[scoped.$index] }}
                  </span>
                  <span v-else>
                    {{ scoped.row.applicationScope.currencySymbol }}
                    {{ scoped.row.specialPrice | numberFilter('--', 0, true) }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 场景2：包含物流费用 -->
          <el-table-column v-if="otherExpenses" label="预估物流费用/台" minWidth="160">
            <template #header>
              <div style="text-align: center;">
                <span>预估物流费用/台</span>
                <el-tooltip content="预估费用中计算的物流费用合计" placement="top-start">
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <div>
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">
                  {{ scoped.row.logisticsExpenses | numberFilter('--', 0, true) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 预估其他费用/台 -->
          <el-table-column v-if="otherExpenses" label="预估其他费用/台" minWidth="160">
            <template #header>
              <div style="text-align: center;">
                <span>预估其他费用/台</span>
                <el-tooltip content="预估费用中计算的其他费用合计" placement="top-start">
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <div>
                <div style="display: inline-block">
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div style="display: inline-block">
                  {{ scoped.row.otherExpenses | numberFilter('--', 0, true) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 预估综合特价/台 -->
          <el-table-column v-if="otherExpenses" label="预估综合特价/台" minWidth="160">
            <template #header>
              <div style="text-align: center;">
                <span>预估综合特价/台</span>
                <el-tooltip
                  content="限制输入正整数，EXW设备特价/台=预估综合特价/台-预估物流费用/台-预估其他费用/台"
                  placement="top-start"
                >
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <el-form-item
                :prop="`tableData.${scoped.$index}.total`"
                :rules="[
                  { required: true, message: '请输入预估综合特价', trigger: ['blur', 'change'] },
                ]"
              >
                <div style="display: flex; align-items: center;">
                  <span style="margin-right: 4px;">{{
                    scoped.row.applicationScope.currencySymbol
                  }}</span>
                  <thous-number
                    style="width: 100px"
                    :value="
                      typeof scoped.row.total === 'string'
                        ? Number(scoped.row.total)
                        : scoped.row.total
                    "
                    @input="scoped.row.total = $event"
                    :precision="0"
                    size="mini"
                    :min="1"
                    placeholder=""
                    @change="totalChange(scoped.row, scoped.$index)"
                  />
                </div>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleCancel">取消</el-button>
      <el-button size="mini" type="primary" @click="doConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" src="./AdjustDialog.ts"></script>

<style lang="scss" scoped>
// 表单项样式调整
.table-form-item {
  margin-bottom: 0;

  /deep/ .el-form-item__error {
    position: static;
    padding-top: 4px;
    font-size: 12px;
    line-height: 1.2;
  }
}

::v-deep .el-dialog {
  margin-top: 0px !important;
}
::v-deep .el-dialog__body {
  padding-top: 10px;
}
::v-deep .el-form-item {
  margin-top: 18px;
}
.content-text {
  color: #d7d7d7;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.icon-info {
  margin-top: 4px;
  color: #a1b0c8;
  font-size: 14px;
  margin: 0px 4px;
  &:hover {
    color: $--color-primary;
  }
}
</style>
