/*
 * @Author: 张文轩
 * @Date: 2024-06-27 11:12:58
 * @LastEditTime: 2024-12-04 14:09:10
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\SpecialApply\SpecialApplyDetail.ts
 * 记得注释
 */
import { Component } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import { AbstractEdit } from '@/views/AbstractEdit';
import { RouterNames } from '@/router/modules';
import { SpecialApplyPermissions } from '@/router/permissions/SpecialApplyPermissions';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import ImportFileApi from '@/http/file/ImportFileApi';
import AnnexApi from '@/http/data/controller/annex/AnnexApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
import SpecialApplyBillOtherExpenesApi from '@/http/price/controller/specialapply/otherexpenses/SpecialApplyBillOtherExpenesApi';
import SpecialApplyProcessApi from '@/http/price/controller/specialapply/approve/SpecialApplyProcessApi';
// MODELS
import { EditMode } from '@/model/local/components/page/EditMode';
import BQuotationApplyPayInfo from '@/model/remote/price/controller/quotationapply/other/BQuotationApplyPayInfo';
import BpfmHiTaskInstVO from 'model/remote/price/model/vo/bpfm/BpfmHiTaskInstVO';
import BSaveAnnexRequest from '@/model/remote/data/controller/annex/BSaveAnnexRequest';
import { AnnexOwnerType } from '@/model/remote/data/model/vo/annex/AnnexOwnerType';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyApplicationScopePayType from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyApplicationScopePayType';
import ApplicationScopeExpenseRate from '@/model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRate';
import BSaveCostPriceRequest from '@/model/remote/price/controller/quotationapply/approve/BSaveCostPriceRequest';
import MatLineCostPrice from '@/model/remote/price/controller/quotationapply/approve/MatLineCostPrice';
import BSaveSpecialApplyBillDiscountRequest from '@/model/remote/price/controller/specialapply/approve/BSaveSpecialApplyBillDiscountRequest';
import CompleteSpecialApplyBillUserTaskRequest from '@/model/remote/price/api/specialapply/bill/CompleteSpecialApplyBillUserTaskRequest';
import BSpecialApplyReviewRequest from '@/model/remote/price/controller/specialapply/approve/BSpecialApplyReviewRequest';
import SpecialApplySupplementPrice from '@/model/remote/price/api/specialapply/priceoverview/SpecialApplySupplementPrice';
import BSpecialApplyAdjustRequest from '@/model/remote/price/controller/specialapply/approve/BSpecialApplyAdjustRequest';
import SpecialApplyPriceOverviewMatLineV2 from 'model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewMatLineV2';
import SpecialApplyFeeDetail from 'model/remote/price/api/specialapply/priceoverview/SpecialApplyFeeDetail';
import BGetExchangeRateTypeRequest from 'model/remote/price/controller/profitanalysis/BGetExchangeRateTypeRequest';
import SpecialApplyProfitAnalysissApi from '@/http/price/controller/profitanalysis/SpecialApplyProfitAnalysissApi';
import { SpecialApplyProfitAnalysissExchangeRateType } from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysissExchangeRateType';
import { BillType } from 'model/remote/price/api/approvenode/BillType';
import SpecialApplyProfitAnalysisQueryRequest from 'model/remote/price/controller/profitanalysis/SpecialApplyProfitAnalysisQueryRequest';
import SpecialApplyProfitAnalysissExchangeRateLine from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysissExchangeRateLine';

// COMPONENTES
import DetailPage from '@/components/page/DetailPage/index.vue';
import StatePopover from '@/components/state-popover/StatePopover.vue';
import Panel from '@/components/panel/Panel.vue';
import ViewItem from '@/components/form/view-item/index.vue';
import FreightDrawer from '../ContractQuotation/cmp/drawer/FreightDrawer.vue';
import ApproveShippingDiscountsDrawer from '../ContractQuotation/cmp/drawer/ApproveShippingDiscountsDrawer.vue';
import OtherFreightLineDrawer from '../ContractQuotation/cmp/drawer/OtherFreightLineDrawer.vue';
import ShipmentScenariosDrawer from '../ContractQuotation/cmp/drawer/ShipmentScenariosDrawer.vue';
import DialogCheck from './cmp/step3/DialogCheck.vue';
import OtherDialog from './cmp/OtherDialog.vue';
import Dialog from 'components/dialog/Dialog';
import AnnexDialog from '../ContractQuotation/cmp/drawer/AnnexDialog.vue';
import SpecialApplyDescriptionDrawerView from './cmp/step3/SpecialApplyDescriptionDrawerView.vue';
import SpecialSubmitDialog from './cmp/drawer/SpecialSubmitDialog.vue';
import EnterCostPriceDialog from './cmp/drawer/EnterCostPriceDialog.vue';
import HandleOperateDialog from '../ContractQuotation/cmp/drawer/HandleOperateDialog.vue';
import AgreeDialog from './cmp/drawer/AgreeDialog.vue';
import AgainCheckDialog from './cmp/drawer/AgainCheckDialog.vue';
import FilePreview from '@/views/components/file/FilePreview.vue';
import AdjustDialog from './cmp/drawer/AdjustDialog.vue';
import ExpenseDetails from './cmp/detailTab/ExpenseDetails.vue';
import ApprovalInformationDrawer from './cmp/step3/ApprovalInformationDrawer.vue';
import SpecialGrossProfitAnalysisDrawer from './cmp/drawer/SpecialGrossProfitAnalysisDrawer.vue';

@Component({
  name: 'SpecialApplyDetail',
  components: {
    DetailPage,
    StatePopover,
    Panel,
    ViewItem,
    FreightDrawer,
    ApproveShippingDiscountsDrawer,
    OtherFreightLineDrawer,
    ShipmentScenariosDrawer,
    DialogCheck,
    OtherDialog,
    SpecialApplyDescriptionDrawerView,
    ExpenseDetails,
    ApprovalInformationDrawer,
    SpecialGrossProfitAnalysisDrawer,
  },
})
export default class SpecialApplyDetail extends AbstractEdit {
  entity: SpecialApplyBill = new SpecialApplyBill();
  activeName: string = 'application-instructions';
  activeNameEx: string = 'normal';
  permissions = SpecialApplyPermissions;
  actions: any = []; // 操作按钮列表
  loading: boolean = false;
  fileList3: any[] = []; // 普通附件
  loading3: boolean = false;
  fileList4: any[] = []; // 保密附件
  loading4: boolean = false;
  tipInfo: string = ''; // 顶部提示信息
  tabData: any[] = [];
  paymentEntity: BQuotationApplyPayInfo = new BQuotationApplyPayInfo();
  totalAmount: number = 0; // 总金额
  ObjectUtil = ObjectUtil;
  PrecisionUtil = PrecisionUtil;
  paymentTypeList: any[] = []; // 付款方式级联列表
  qtyTotal: number = 0;
  bpfmHiTaskInstList: BpfmHiTaskInstVO[] = []; // 审批信息列表
  grossProfitAnalysisLoading: boolean = false; // 毛利分析tab  loading
  grossProfitAnalysisInfo: SpecialApplyFeeDetail = new SpecialApplyFeeDetail(); // 毛利分析列表
  tableDataSAP: any[] = [];
  tableSpanSAP: any = {};
  showGrossProfitAnalysisDrawer: boolean = false; // 是否展示毛利分析详情抽屉
  grossProfitAnalysisDrawerInfo: any = null; // 毛利分析详情抽屉信息
  exchangeRateTypeList: any[] = []; // 汇率类型列表
  // 是否展示审批信息
  showApprovalInformationDrawer: boolean = false;
  previewList = [
    'doc',
    'docx',
    'xls',
    'xlsx',
    'xlsm',
    'ppt',
    'pptx',
    'csv',
    'tsv',
    'dotm',
    'xlt',
    'xltm',
    'dot',
    'xlam',
    'dotx',
    'xla',
    'pages',
  ]; // 预览支持文件类型
  action: any = {}; // 当前操作
  payTypeData: SpecialApplyApplicationScopePayType[] = [];
  dialogDialogCheck: boolean = false;
  childrenEntity: ApplicationScopeExpenseRate[] = [];
  childrenType: string = '';
  childrenTitle: string = '';
  days: number = 0;
  // OtherDialogShow: boolean = false; // 查看其他费用明细
  // showFillDrawer: boolean = false; // 展示特价申请说明

  tableSpan: any = {};

  get uploadNormalFile() {
    return this.$store.state.permissions.includes(this.permissions.uploadNormalFile);
  }
  get uploadPrivacyFile() {
    return this.$store.state.permissions.includes(this.permissions.uploadPrivacyFile);
  }

  // 是否有查看毛利分析权限
  get isHasviewProfit() {
    return this.$store.state.permissions.includes(this.permissions.viewProfit);
  }

  // 费用明细tab是否展示
  get showFeeDetails() {
    return this.$store.state.permissions.includes(this.permissions.feeDetailsView);
  }
  // 价格信息tab是否展示
  get showPriceInfo() {
    if (this.entity.exportTypeId === 'self' && this.entity.saleMode === 'buyOut') {
      return true;
    }
    return false;
  }
  // 获取费用明细表格原始行数（按特价适用范围统计）
  get materialRowCount() {
    // 如果不是费用明细页，返回0
    if (!this.entity.specialType || this.activeName !== 'first') {
      return 0;
    }

    const expenseDetailsRef = this.$refs.ExpenseDetails;
    if (expenseDetailsRef && expenseDetailsRef.tableData) {
      return expenseDetailsRef.tableData.length;
    }

    // 组件尚未加载完成或数据尚未获取时，返回0
    return 0;
  }
  mounted() {
    super.mounted();
  }
  viewEntity() {
    SpecialApplyBillApi.get(this.id).then((res) => {
      if (res.data) {
        this.entity = res.data;
        this.actions = this.getActions(res.data);

        // 强制测试：确保归档按钮存在
        const hasArchiveButton = this.actions.some((action) => action.name === '归档');
        if (!hasArchiveButton) {
          console.log('强制添加归档按钮');
          this.actions.unshift({
            name: '归档',
            taskId: 'archive_task',
            taskCode: 'priceSpecialistReview',
            key: 'archive',
            commentRequired: false,
          });
        }

        // 查询附件
        this.doSearch(3);
        this.doSearch(4);
        this.getPayType();
      }
    });
  }
  getPayType() {
    // 查询付款信息
    SpecialApplyBillOtherExpenesApi.getPayType(this.id)
      .then((res) => {
        if (res.data) {
          // this.entity = res.data;
          // this.form.lines = res.data ? res.data : [];
          this.payTypeData = res.data;
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  paymentTypeChange(row, index) {
    // 清空特殊信保费率
    // row.applyCreditInsuranceRate = null;
    let paymentType: any = {};
    this.paymentTypeList.find((i) => {
      i.lines.find((j) => {
        if (j.id === row.paymentTypeId) {
          paymentType = j;
        }
      });
    });
    row.paymentTypeName = paymentType.name; // 中文名
    row.paymentTypeNameEn = paymentType.nameEn; // 英文名
  }
  getTableData(result: SpecialApplyPriceOverviewMatLineV2[]) {
    let arr: any = [];
    let spanArr: any = {}; // 表格数据合并
    result.forEach((item, index) => {
      if (index === 0) {
        spanArr[index] = item.exwPriceList.length;
      } else {
        let lastNum = 0;
        for (let key in spanArr) {
          lastNum = Number(key) + spanArr[key];
        }
        spanArr[lastNum] = item.exwPriceList.length;
      }
      if (item.exwPriceList && item.exwPriceList.length > 0) {
        item.exwPriceList.forEach((priceItem) => {
          arr.push({
            ...item,
            ...priceItem,
            specialApplyMat: item,
            applicationScope: priceItem.applicationScope,
            // 初始化默认值：EXW设备特价 = 设备指导价，调价差值 = 空
            specialPrice: Number(priceItem.specialPrice || priceItem.exwPrice),
            differAmt: priceItem.differAmt ? Number(priceItem.differAmt) : null,
            mergeIndex: index,
          });
        });
      }
    });
    this.tabData = arr;
    this.tableSpan = spanArr;
  }
  specialPriceChange(item) {
    if (item.specialPrice) {
      if (item.specialPrice > item.exwPrice) {
        item.specialPrice = item.exwPrice;
        item.adjustRange = 100;
      } else {
        item.adjustRange = (((item.exwPrice - item.specialPrice) / item.exwPrice) * 100).toFixed(2);
      }
    } else {
      item.adjustRange = 1;
    }
  }
  // 获取操作按钮
  getActions(row) {
    // 获取流程中心返回按钮组
    let taskArr: any = row.userTaskOutGoings || [];
    // 根据状态及审批节点判断要展示的按钮
    let arr: any = [];
    // 草拟
    if (row.state === 'draft') {
      // 草拟（无值）
      if (!row.taskStateCode) {
        arr = [
          { name: '编辑' },
          { name: '提交审批' },
          { name: '取消' },
          {
            name: '归档',
            taskId: 'archive_task',
            taskCode: 'priceSpecialistReview',
            key: 'archive',
            commentRequired: false,
          }, // 添加归档按钮
          { name: '上传普通附件' },
          { name: '上传保密附件' },
        ];
      }
    }
    // 审批中
    if (row.state === 'approving') {
      arr = [
        {
          name: '归档',
          taskId: 'archive_task',
          taskCode: 'priceSpecialistReview',
          key: 'archive',
          commentRequired: false,
        }, // 添加归档按钮
        { name: '上传普通附件' },
        { name: '上传保密附件' },
      ];
    }
    // 审批通过
    if (row.state === 'approved') {
      arr = [
        { name: '作废' },
        {
          name: '归档',
          taskId: 'archive_task',
          taskCode: 'priceSpecialistReview',
          key: 'archive',
          commentRequired: false,
        }, // 添加归档按钮
        // { name: '导出毛利分析表' },
        { name: '上传普通附件' },
        { name: '上传保密附件' },
      ];
    }
    // 已驳回
    if (row.state === 'reject') {
      if (
        [
          'subsidiaryCeoApprovalReturn', // 子公司总经理驳回
          'priceSpecialistApproveReturn', // 价格专员检查驳回
          'priceManagementCommitteeReturn', // 价格管理委员会驳回
        ].includes(row.taskStateCode)
      ) {
        arr = [
          { name: '编辑' },
          { name: '作废' },
          {
            name: '归档',
            taskId: 'archive_task',
            taskCode: 'priceSpecialistReview',
            key: 'archive',
            commentRequired: false,
          }, // 添加归档按钮
          { name: '上传普通附件' },
          { name: '上传保密附件' },
        ];
      }
      // 国际分管领导初审驳回
      // 国际分管领导复审驳回
      if (
        row.taskStateCode === 'internationalLeaderApprovalReturn' ||
        row.taskStateCode === 'internationalLeaderReviewReturn'
      ) {
        arr = [
          { name: '作废' },
          {
            name: '归档',
            taskId: 'archive_task',
            taskCode: 'priceSpecialistReview',
            key: 'archive',
            commentRequired: false,
          }, // 添加归档按钮
          { name: '上传普通附件' },
          { name: '上传保密附件' },
        ];
      }
    }
    // 权限过滤
    let stateArr = arr.filter((item) => {
      if (item.name === '编辑') {
        return (
          this.$store.state.permissions.includes(this.permissions.edit) &&
          this.$store.state.userInfo.employeeCode === row.createUser
        );
      }
      if (item.name === '提交审批') {
        return (
          this.$store.state.permissions.includes(this.permissions.submit) &&
          this.$store.state.userInfo.employeeCode === row.createUser
        );
      }
      if (item.name === '取消') {
        return (
          this.$store.state.permissions.includes(this.permissions.cancel) &&
          this.$store.state.userInfo.employeeCode === row.createUser
        );
      }
      if (item.name === '归档') {
        // 归档按钮始终显示，不进行权限过滤
        return true;
      }
      if (item.name === '上传普通附件') {
        return this.$store.state.permissions.includes(this.permissions.uploadNormalFile);
      }
      if (item.name === '上传保密附件') {
        return this.$store.state.permissions.includes(this.permissions.uploadPrivacyFile);
      }
      if (item.name === '作废') {
        return (
          this.$store.state.permissions.includes(this.permissions.abort) &&
          this.$store.state.userInfo.employeeCode === row.createUser
        );
      }
      if (item.name === '导出毛利分析表') {
        return this.$store.state.permissions.includes(this.permissions.exportProfit);
      }
      return true;
    });
    // 强制添加归档按钮，确保始终显示
    const finalActions = [...taskArr, ...stateArr];
    const hasArchiveButton = finalActions.some((action) => action.name === '归档');
    if (!hasArchiveButton) {
      finalActions.unshift({
        name: '归档',
        taskId: 'archive_task',
        taskCode: 'priceSpecialistReview',
        key: 'archive',
        commentRequired: false,
      });
    }
    return finalActions;
  }

  // 获取按钮颜色type
  getActionType(action) {
    let primaryArr = [
      '提交审批',
      '编辑',
      '填入费率',
      '定制装运方案',
      '填入询价结果',
      '提交',
      '填写成本',
      '填写防护费',
      '通过',
      '审批运费折扣',
      '添加意见',
      '转审',
      '同意',
      '归档', // 添加归档按钮为主要按钮样式
    ];
    let dangerArr = ['取消', '驳回', '作废'];
    if (primaryArr.includes(action.name)) {
      return 'primary';
    }
    if (dangerArr.includes(action.name)) {
      return 'danger';
    }
    return '';
  }

  doAction(action) {
    let row = this.entity;
    if (action.name === '编辑') {
      this.doEdit(row);
    }
    if (action.name === '提交') {
      this.doSubmit(row, action);
    }
    if (action.name === '提交审批') {
      this.doSubmit(row, null);
    }
    if (action.name === '取消') {
      this.$confirm('确认取消所选特价申请吗？', '提示').then(() => {
        if (row.state === 'draft') {
          // 如果是草拟状态
          let params: any = { id: row.id, version: row.version };
          this.doCancel(params);
        } else {
          this.handleOperate('', row, action);
        }
      });
    }
    if (action.name === '填写成本') {
      SpecialApplyBillApi.getMatLine(this.id)
        .then((res) => {
          let data = res.data || [];
          new Dialog(EnterCostPriceDialog, {
            entity: row,
            result: data,
            onConfirm: (result) => {
              const body = new BSaveCostPriceRequest();
              body.id = row.id!;
              body.version = row.version!;
              body.userTaskOutGoing = action;
              body.matLineCostPrices = result.map((item) => {
                let params = new MatLineCostPrice();
                params.id = item.id;
                params.costPrice = item.costPrice;
                return params;
              });
              SpecialApplyProcessApi.saveCostPrice(body)
                .then((res) => {
                  this.$message.success('填写成功');
                  this.initEntity();
                })
                .catch((rej) => {
                  this.$message.error(rej.message);
                });
            },
          }).show();
        })
        .catch((rej) => {
          this.$message.error(rej.message);
        });
    }
    if (action.name === '同意') {
      new Dialog(AgreeDialog, {
        discount: row.discount,
        id: row.id,
        taskStateCode: row.taskStateCode,
        onConfirm: (data) => {
          let body = new BSaveSpecialApplyBillDiscountRequest();
          body.id = row.id!;
          body.version = row.version!;
          body.userTaskOutGoing = action;
          body.discount = data.discount;
          body.comment = data.comment;
          SpecialApplyProcessApi.saveBillDiscount(body)
            .then((res) => {
              this.$message.success('操作成功');
              this.initEntity();
            })
            .catch((rej) => {
              this.$message.error(rej.message);
            })
            .finally(() => {
              this.loading = false;
            });
        },
      }).show();
    }
    if (action.name === '通过') {
      new Dialog(HandleOperateDialog, {
        contentText: '-可以输入您对本申请的一些补充意见',
        onConfirm: (data: Nullable<string>) => {
          this.handleOperate(data, row, action);
        },
      }).show();
    }
    if (action.name === '驳回') {
      new Dialog(HandleOperateDialog, {
        title: '驳回申请',
        contentText: '-请输入您驳回此申请的原因，指导申请人改进',
        onConfirm: (data: Nullable<string>) => {
          this.handleOperate(data, row, action);
        },
      }).show();
    }
    if (action.name === '添加意见') {
      SpecialApplyBillApi.listOwnerProductGroupId(this.id)
        .then((res) => {
          if (!res.data || res.data.length === 0) {
            this.$message.warning('无产品线');
            return;
          }
          const form: any = res.data.reduce((pre, cur) => {
            pre[cur.id!] = '';
            return pre;
          }, {});
          new Dialog(HandleOperateDialog, {
            title: '添加意见',
            contentText:
              '-请输入您对本申请的意见，后续审批环节将郑重参考您的宝贵意见；至少填写一个',
            ProductLineList: res.data,
            form, // 表单数据
            onConfirm: (data: Nullable<string>) => {
              this.handleOperate(data, row, action);
            },
          }).show();
        })
        .catch((rej) => {
          this.$message.error(rej.message);
        });
    }
    if (action.name === '转审') {
      new Dialog(HandleOperateDialog, {
        title: '转审',
        onConfirm: (data: Nullable<string>) => {
          this.handleOperate(data, row, action);
        },
      }).show();
    }
    if (action.name === '作废') {
      this.$confirm('确认作废所选报价申请吗？', '提示').then(() => {
        let params: any = { id: row.id, version: row.version };
        this.doAbort(params);
      });
    }
    if (action.name === '归档') {
      SpecialApplyBillApi.getPriceOverview(row.id!)
        .then((res) => {
          new Dialog(AgainCheckDialog, {
            entity: res.data || [],
            specialType: row.specialType,
            otherExpenses: row.otherExpenses,
            onConfirm: (data, comment) => {
              let body = new BSpecialApplyReviewRequest();
              body.id = row.id!;
              body.version = row.version!;
              body.userTaskOutGoing = action;
              body.comment = comment;
              body.matLines = data;
              SpecialApplyProcessApi.review(body)
                .then((res) => {
                  this.$message.success('操作成功');
                  this.initEntity();
                })
                .catch((rej) => {
                  this.$message.error(rej.message);
                });
            },
          }).show();
        })
        .catch((rej) => {
          this.$message.error(rej.message);
        });
    }
    if (action.name === '调整') {
      SpecialApplyBillApi.getPriceOverview(row.id!)
        .then((res) => {
          new Dialog(AdjustDialog, {
            entity: res.data || [],
            specialType: row.specialType,
            otherExpenses: row.otherExpenses,
            onConfirm: (data) => {
              let body = new BSpecialApplyAdjustRequest();
              body.id = row.id!;
              body.version = row.version!;
              body.userTaskOutGoing = action;
              body.matLines = data;
              SpecialApplyProcessApi.adjust(body)
                .then((res) => {
                  this.$message.success('操作成功');
                  this.initEntity();
                })
                .catch((rej) => {
                  this.$message.error(rej.message);
                });
            },
          }).show();
        })
        .catch((rej) => {
          this.$message.error(rej.message);
        });
    }
    if (action.name === '导出毛利分析表') {
    }
    if (action.name === '上传普通附件') {
      new Dialog(AnnexDialog, {
        model: 'special',
        row: row,
        success: () => {
          this.doSearch(3);
          this.doSearch(4);
        },
      }).show();
    }
    if (action.name === '上传保密附件') {
      new Dialog(AnnexDialog, {
        model: 'special',
        row: row,
        success: () => {
          this.doSearch(3);
          this.doSearch(4);
        },
      }).show();
    }
  }
  // 编辑
  doEdit(row) {
    this.$router.push({
      name: RouterNames.specialApplyEdit,
      query: {
        id: row.id,
        editMode: EditMode.edit,
      },
    });
  }
  // 提交
  doSubmit(row, action) {
    let params = {
      id: row.id,
      version: row.version,
      outGoing: action,
    };
    new Dialog(SpecialSubmitDialog, {
      params: params,
      method: this.submitMethod,
      sucess: () => {
        this.initEntity();
      },
    }).show();
  }
  submitMethod(params) {
    return SpecialApplyBillApi.submit(params);
  }
  // 取消 - 草拟状态
  doCancel({ id, version }) {
    this.loading = true;
    let params = {
      idVersionPair: {
        id,
        version,
      },
    };
    SpecialApplyBillApi.cancel(params)
      .then((res) => {
        this.$message.success('取消成功');
        this.initEntity();
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  // 作废
  doAbort({ id, version }) {
    this.loading = true;
    let params = {
      idVersionPair: {
        id,
        version,
      },
    };
    SpecialApplyBillApi.abort(params)
      .then((res) => {
        this.$message.success('作废成功');
        this.initEntity();
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  // 处理【通过】【驳回】【添加意见】【无意见】【取消】等操作逻辑
  handleOperate(data: Nullable<string>, row: SpecialApplyBill, action) {
    const body = new CompleteSpecialApplyBillUserTaskRequest();
    body.id = row.id!;
    body.version = row.version!;
    body.userTaskOutGoing = action;
    body.comment = data;
    SpecialApplyBillApi.completeUserTask(body)
      .then((res) => {
        this.$message.success('操作成功');
        this.initEntity();
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }
  // 切换tab
  async handleClick(tab) {
    // 选择了审批信息tab
    // if (tab.name === 'fourth') {
    //   const { data } = await SpecialApplyBillApi.listHistoryTask(this.entity.id!);
    //   this.bpfmHiTaskInstList = data || [];
    // }
    if (tab.name === 'profitability-analysis') {
      await this.getSpecialApplyProfitAnalysis();
      await this.getExchangeRateType();
    }
  }
  // 获取特价申请毛利分析信息（SAP）
  getSpecialApplyProfitAnalysis() {
    this.grossProfitAnalysisLoading = true;
    const billNum: Nullable<string> = this.entity.id!;
    SpecialApplyBillApi.getFeeDetails(billNum)
      .then((res) => {
        this.grossProfitAnalysisInfo = res.data || new SpecialApplyFeeDetail();
        this.getTableDataSAP(this.grossProfitAnalysisInfo);
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.grossProfitAnalysisLoading = false;
      });
  }

  // 格式化用于表格数据源的毛利分析信息
  getTableDataSAP(result: SpecialApplyFeeDetail) {
    let arr: any = [];
    let spanArr: any = {};
    result.lines.forEach((item, index) => {
      if (index === 0) {
        spanArr[index] = item.specialApplyMat?.exwPriceList.length;
      } else {
        let lastNum = 0;
        for (let key in spanArr) {
          lastNum = Number(key) + spanArr[key];
        }
        spanArr[lastNum] = item.specialApplyMat?.exwPriceList.length;
      }
      if (item.specialApplyMat?.exwPriceList && item.specialApplyMat?.exwPriceList.length > 0) {
        item.specialApplyMat?.exwPriceList.forEach((priceItem) => {
          arr.push({
            ...item.specialApplyMat,
            ...priceItem,
            specialApplyMat: item.specialApplyMat,
            applicationScope: priceItem.applicationScope,
            // 初始化默认值：EXW设备特价 = 设备指导价，调价差值 = 空
            specialPrice: Number(priceItem.specialPrice || priceItem.exwPrice),
            differAmt: priceItem.differAmt ? Number(priceItem.differAmt) : null,
            adjustRange: this.calculateAdjustRange(priceItem.adjustRange),
            mergeIndex: index,
            // 单台终端费用相关
            terminalPrice: item.terminalPrice, // 终端价格
            dlrProcurementCost: item.dlrProcurementCost, // 经销商采购成本
            inlandFreight: item.inlandFreight, // 内陆运费
            customsClearanceFee: item.customsClearanceFee, // 清关费
            assemblyFee: item.assemblyFee, // 组装费
            tax: item.tax, // 税费
            portCharge: item.portCharge, // 港杂口岸费
            pdiFee: item.pdiFee, // PDI费用
            commissionFee: item.commissionFee, // 佣金费用
            financingFee: item.financingFee, // 融资费用
            otherFee: item.otherFee, // 其他费用
            otherFeeName: item.otherFeeName, // 其他费用名称
          });
        });
      }
    });

    this.tableDataSAP = arr;
    this.tableSpanSAP = spanArr;
  }

  // 表格合并
  spanMethodSAP({ row, column, rowIndex, columnIndex }) {
    // 对前3列进行合并：整机物料号、物料描述（营销）、物料描述（营销）_英文
    if (columnIndex <= 2) {
      if (this.tableSpanSAP[rowIndex]) {
        return {
          rowspan: this.tableSpanSAP[rowIndex],
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }

  // 计算调价幅度百分比
  calculateAdjustRange(adjustRange) {
    if ((!adjustRange && adjustRange !== 0) || adjustRange < 0) {
      return null;
    }
    return Number(PrecisionUtil.toFixed(adjustRange * 100, 2));
  }

  // 格式化调价幅度显示
  formatAdjustRange(row) {
    // 调价幅度为0时显示0不显示--
    if (row.adjustRange === 0) {
      return '0';
    }
    if (row.adjustRange !== null && row.adjustRange !== undefined) {
      return `-${row.adjustRange}%`;
    }
    return '--';
  }

  // 格式化适用区域
  formatSpecialPriceApplicableArea(row) {
    if (row && row.applicationScope) {
      let applicationScope = row.applicationScope;
      let content = `子公司：${applicationScope.sbsdyName}`;

      if (this.entity.specialType != 'adjustZoneGuidePrice') {
        content += `\n办事处：${applicationScope.ofcName}`;
      }

      content += `\n销往国：${this.formatCtryName(applicationScope)}`;

      if (this.entity.specialType == 'importantProjects') {
        content += `\n客户：${applicationScope.custName}`;
      }

      content += `\n币种：${applicationScope.currencyId}`;

      return content;
    }
    return '--';
  }

  // 格式化销往国
  formatCtryName(applicationScope) {
    if (!applicationScope.ctrys || applicationScope.ctrys.length <= 0) {
      return '--';
    }
    let res: string[] = [];
    for (let ctry of applicationScope.ctrys) {
      for (let applicationScopeCtryItem of ctry.applicationScopeCtry) {
        res.push(applicationScopeCtryItem.name as string);
      }
    }
    if (res.length <= 0) {
      return '--';
    }
    return res.join('、');
  }

  // 获取汇率类型
  getExchangeRateType() {
    this.grossProfitAnalysisLoading = true;
    const body = new BGetExchangeRateTypeRequest();
    body.billNum = this.entity.id;
    body.type = BillType.ltc_special_apply_bill;
    SpecialApplyProfitAnalysissApi.getExchangeRateType(body)
      .then((res) => {
        if (res.data) {
          const mappedList = (res.data.lines || []).map((line) => {
            return {
              ...line,
              label: this.handleRateType(line.type),
            };
          });

          // 按照指定顺序排序：年度预算汇率 > 最新汇率 > 特价申请汇率
          const sortOrder = {
            [SpecialApplyProfitAnalysissExchangeRateType.annualBudget]: 1,
            [SpecialApplyProfitAnalysissExchangeRateType.now]: 2,
            [SpecialApplyProfitAnalysissExchangeRateType.bill]: 3,
          };

          this.exchangeRateTypeList = mappedList.sort((a, b) => {
            const orderA = sortOrder[a.type!] || 999;
            const orderB = sortOrder[b.type!] || 999;
            return orderA - orderB;
          });
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.grossProfitAnalysisLoading = false;
      });
  }

  // 特价申请汇率类型语义化
  handleRateType(type: Nullable<SpecialApplyProfitAnalysissExchangeRateType>) {
    if (type === SpecialApplyProfitAnalysissExchangeRateType.now) {
      return '最新汇率';
    } else if (type === SpecialApplyProfitAnalysissExchangeRateType.bill) {
      return '特价申请汇率';
    } else if (type === SpecialApplyProfitAnalysissExchangeRateType.annualBudget) {
      return '年度预算汇率';
    } else {
      return '--';
    }
  }

  // 打开毛利分析详情抽屉
  viewGrossProfitAnalysisDetail(value) {
    // 毛利分析计算前检查
    const body = new SpecialApplyProfitAnalysisQueryRequest();
    body.billNum = this.entity.id;
    body.specialApplyMatOverviewId = value.id;
    const RateLine = new SpecialApplyProfitAnalysissExchangeRateLine();
    // 固定一个类型即可，一个类型通过则全部类型都能计算，反之都不能
    RateLine.type = SpecialApplyProfitAnalysissExchangeRateType.bill;
    const list = this.exchangeRateTypeList.filter(
      (item) => item.type === SpecialApplyProfitAnalysissExchangeRateType.bill
    );
    if (list.length > 0) {
      RateLine.exchangeRateList = list[0].exchangeRateList;
    }
    body.rateType = RateLine;
    // 调用毛利分析计算前检查接口
    SpecialApplyProfitAnalysissApi.calCheck(body)
      .then((res) => {
        console.log(res);
        this.showGrossProfitAnalysisDrawer = true;
        this.grossProfitAnalysisDrawerInfo = value;
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }

  handleTime(value) {
    return value ? (value / 3600).toFixed(2) : '--';
  }
  // 查看折扣额度使用概述
  // doOtherExpenses() {
  //   this.OtherDialogShow = true;
  // }
  // doSpecialOffer() {
  //   this.showFillDrawer = true;
  // }
  // 查询附件
  doSearch(type) {
    this['loading' + type] = true;
    let paramIn: any = {
      ownerIn: [this.id],
      ownerTypeIn: [AnnexOwnerType.specialApplyApply],
      annexeTypeIn: [type],
    };
    AnnexApi.queryForList(paramIn)
      .then((res) => {
        this['fileList' + type] = res.data || [];
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this['loading' + type] = false;
      });
  }
  // 上传附件
  onUpload(file, type) {
    if (file.size / 1024 / 1024 > 20) {
      return this.$message.error('文件大小不能超过20M');
    }
    this.loading3 = true;
    this.loading4 = true;
    ImportFileApi.fileUpload(file, '')
      .then((res) => {
        if (res.data) {
          let paramIn: BSaveAnnexRequest = new BSaveAnnexRequest();
          paramIn.owner = this.id;
          paramIn.ownerType = AnnexOwnerType.specialApplyApply;
          paramIn.lines = [
            {
              path: res.data.fileUrl,
              fileName: res.data.fileName,
              type: type,
            },
          ];
          AnnexApi.saveAnnexs(paramIn)
            .then((result) => {
              // 刷新数据
              this.doSearch(type);
            })
            .catch((rej) => {
              this.$message.error(rej.message);
            });
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading3 = false;
        this.loading4 = false;
      });
    return false;
  }
  // 上传普通附件
  onUpload1(file) {
    this.onUpload(file, 3);
  }
  // 上传保密附件
  onUpload2(file) {
    this.onUpload(file, 4);
  }
  // 下载
  onDownload(row) {
    let url = row.path;
    const x = new XMLHttpRequest();
    x.open('GET', url, true);
    x.responseType = 'blob';
    x.onload = function(e) {
      const url = window.URL.createObjectURL(x.response);
      const a = document.createElement('a');
      a.href = url;
      a.target = '_blank';
      a.download = row.fileName;
      a.click();
      a.remove();
    };
    x.send();
  }
  // 是否支持预览
  showPreview(row) {
    let windowOpenArr = ['jpg', 'jpeg', 'png', 'pdf'];
    return (
      this.previewList.includes(this.getSuffix(row)) || windowOpenArr.includes(this.getSuffix(row))
    );
  }
  // 预览
  previewFile(row) {
    let url = row.path;
    if (this.previewList.includes(this.getSuffix(row))) {
      new Dialog(FilePreview, {
        title: row.fileName,
        url: url,
      }).show();
    } else {
      if (this.getSuffix(row) == 'pdf') {
        window.open(url);
      } else {
        let str =
          '<!DOCTYPE html><html style="height: 100%; background-color: #0E0E0E"><head><title>' +
          row.fileName +
          '</title></head><body style="display: flex; justify-content: center; align-items: center; height: 100%;"><img style src="' +
          url +
          '" /></body></html>';
        let a = window.open('');
        a?.document.write(str);
      }
    }
  }
  // 获取文件后缀
  getSuffix(item) {
    let arr = item.fileName.split('.');
    let name = arr[arr.length - 1];
    return name;
  }
  // 删除
  onDelete(row) {
    this.$confirm('确定删除该附件吗？', '提示', {}).then(() => {
      AnnexApi.delete(row.id)
        .then((res) => {
          this.$message.success('删除成功');
          this.doSearch(row.type);
        })
        .catch((rej) => {
          this.$message.error(rej.message);
        });
    });
  }
  //查看远期贴现率和信保费率
  checkSat(row, type, title, days) {
    this.dialogDialogCheck = true;
    this.childrenEntity = row;
    this.childrenType = type;
    this.childrenTitle = title;
    this.days = Number(days);
  }
  // 返回列表
  doBackList() {
    this.openPage(RouterNames.specialApplyList, {});
    // this.$router.push({
    //   name: RouterNames.specialApplyList,
    // });
  }
  // 查看审批信息
  doApprovalInformation() {
    this.showApprovalInformationDrawer = true;
  }
  // 表格合并
  spanMethod({ row, column, rowIndex, columnIndex }) {
    // 对前3列进行合并：整机物料号、物料描述（营销）、物料描述（营销）_英文
    if (columnIndex <= 2) {
      if (this.tableSpan[rowIndex]) {
        return {
          rowspan: this.tableSpan[rowIndex],
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }
}
